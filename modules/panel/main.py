from fabric.system_tray.widgets import System<PERSON>ray, SystemTrayItem
from fabric.utils import exec_shell_command, get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.datetime import DateTime
from fabric.widgets.label import Label
from fabric.widgets.revealer import Revealer
from fabric.widgets.eventbox import EventBox
from gi.repository import Gtk, GLib, Gdk
from fabric.widgets.svg import Svg
from modules.panel.components.indicators import Indicators
from modules.panel.components.menubar import MenuBar
from utils.wayland import WaylandWindow as Window
from utils.occlusion import check_occlusion
import config.data as data

original_do_update_properties = SystemTrayItem.do_update_properties


def patched_do_update_properties(self, *_):
    # Try default GTK theme first
    icon_name = self._item.icon_name
    attention_icon_name = self._item.attention_icon_name

    if self._item.status == "NeedsAttention" and attention_icon_name:
        preferred_icon_name = attention_icon_name
    else:
        preferred_icon_name = icon_name

    # Try to load from default GTK theme
    if preferred_icon_name:
        try:
            default_theme = Gtk.IconTheme.get_default()
            if default_theme.has_icon(preferred_icon_name):
                pixbuf = default_theme.load_icon(
                    preferred_icon_name, self._icon_size, Gtk.IconLookupFlags.FORCE_SIZE
                )
                if pixbuf:
                    self._image.set_from_pixbuf(pixbuf)
                    # Set tooltip
                    tooltip = self._item.tooltip
                    self.set_tooltip_markup(
                        tooltip.description or tooltip.title or self._item.title.title()
                        if self._item.title
                        else "Unknown"
                    )
                    return
        except:
            pass
    original_do_update_properties(self, *_)


SystemTrayItem.do_update_properties = patched_do_update_properties


class Panel(Window):
    def __init__(
        self,
    ):
        super().__init__(
            name="bar",
            layer="top",
            anchor="left top right",
            exclusivity="auto",
            visible=False,
        )

        # Autohide functionality
        self.auto_hide_enabled = data.PANEL_AUTO_HIDE
        self.is_mouse_over_panel_area = False
        self.hide_id = None
        self.panel_height = 32  # Default panel height, adjust as needed
        self._prevent_occlusion = False

        self.menubar = MenuBar()

        self.imac = Button(
            name="panel-button",
            child=Svg(
                size=24,
                svg_file=get_relative_path("../../config/assets/icons/logo.svg"),
            ),
            on_clicked=lambda *_: self.menubar.show_system_dropdown(self.imac)
        )
        self.notch_spot = Box(
            name="notch-spot",
            size=(200, 24),
            h_expand=True,
            v_expand=True,
            children=Label(label="notch"),
        )

        self.tray = SystemTray(name="system-tray", spacing=4, icon_size=20)

        self.tray_revealer = Revealer(
            name="tray-revealer",
            child=self.tray,
            child_revealed=False,
            transition_type="slide-left",
            transition_duration=300,
        )

        self.chevron_button = Button(
            name="panel-button",
            child=Svg(
                size=18,
                svg_file=get_relative_path("../../config/assets/icons/chevron-right.svg"),
            ),
            on_clicked=self.toggle_tray,
        )

        self.indicators = Indicators()

        self.search = Button(
            name="panel-button",
            on_clicked=lambda *_: self.search_apps(),
            child=Svg(
                size=20,
                svg_file=get_relative_path("../../config/assets/icons/search.svg"),
            ),
        )

        self.controlcenter = Button(
            name="panel-button",
            child=Svg(
                size=24,
                svg_file=get_relative_path("../../config/assets/icons/control-center.svg"),
            ),
        )

        self.panel_content = CenterBox(
            name="panel",
            start_children=Box(
                name="modules-left",
                children=[
                    self.imac,
                    self.menubar,
                ],
            ),
            # center_children=Box(
            #     name="modules-center",
            #     children=self.notch_spot,
            # ),
            end_children=Box(
                name="modules-right",
                spacing=4,
                orientation="h",
                children=[
                    self.tray_revealer,
                    self.chevron_button,
                    self.indicators,
                    self.search,
                    self.controlcenter,
                    DateTime(name="date-time", formatters=["%a %b %d %I:%M"]),
                ],
            ),
        )

        # Create revealer for autohide functionality
        self.panel_revealer = Revealer(
            name="panel-revealer",
            transition_type="slide-down",
            transition_duration=250,
            child_revealed=not self.auto_hide_enabled,  # Show by default if autohide is disabled
            child=self.panel_content,
        )

        # Create hover activator for autohide
        self.hover_activator = EventBox()
        if self.auto_hide_enabled:
            self.hover_activator.set_size_request(-1, 2)  # Thin strip at top for activation

        # Connect hover events
        self.hover_activator.connect("enter-notify-event", self._on_hover_enter)
        self.hover_activator.connect("leave-notify-event", self._on_hover_leave)
        self.panel_content.connect("enter-notify-event", self._on_panel_enter)
        self.panel_content.connect("leave-notify-event", self._on_panel_leave)

        # Main container
        self.children = Box(
            orientation="v",
            children=[self.hover_activator, self.panel_revealer],
        )

        # Set up autohide functionality
        if self.auto_hide_enabled:
            self.setup_autohide()

        return self.show_all()
    
    def search_apps(self):
        cmd = "fabric-cli exec modus 'launcher.show_launcher()'"
        exec_shell_command(cmd)

    def toggle_tray(self, *_):
        current_state = self.tray_revealer.child_revealed
        self.tray_revealer.child_revealed = not current_state

        if self.tray_revealer.child_revealed:
            self.chevron_button.get_child().set_from_file(get_relative_path("../../config/assets/icons/chevron-left.svg"))
        else:
            self.chevron_button.get_child().set_from_file(get_relative_path("../../config/assets/icons/chevron-right.svg"))

    def setup_autohide(self):
        """Set up autohide functionality for the panel"""
        # Start the occlusion check timer
        GLib.timeout_add(250, self.check_occlusion_state)  # Check every 250ms like dock

        # Initially hide the panel if autohide is enabled
        self.panel_revealer.set_reveal_child(False)

    def _on_hover_enter(self, *args):
        """Handle mouse entering the hover activator area"""
        self.is_mouse_over_panel_area = True
        if self.hide_id:
            GLib.source_remove(self.hide_id)
            self.hide_id = None
        self.panel_revealer.set_reveal_child(True)

    def _on_hover_leave(self, *args):
        """Handle mouse leaving the hover activator area"""
        self.is_mouse_over_panel_area = False
        self.delay_hide()

    def _on_panel_enter(self, widget, event):
        """Handle mouse entering the panel content area"""
        self.is_mouse_over_panel_area = True
        if self.hide_id:
            GLib.source_remove(self.hide_id)
            self.hide_id = None
        self.panel_revealer.set_reveal_child(True)
        return True

    def _on_panel_leave(self, widget, event):
        """Handle mouse leaving the panel content area"""
        if event.detail == Gdk.NotifyType.INFERIOR:
            return False

        self.is_mouse_over_panel_area = False
        self.delay_hide()
        return True

    def delay_hide(self):
        """Start the hide timer"""
        if self.hide_id:
            GLib.source_remove(self.hide_id)
        self.hide_id = GLib.timeout_add(250, self.hide_panel_if_not_hovered)

    def hide_panel_if_not_hovered(self):
        """Hide the panel if mouse is not over it and conditions are met"""
        self.hide_id = None
        if (
            not self.is_mouse_over_panel_area
            and not self._prevent_occlusion
        ):
            occlusion_region = ("top", self.panel_height)
            if check_occlusion(occlusion_region):
                self.panel_revealer.set_reveal_child(False)
        return False

    def check_occlusion_state(self):
        """Check if the panel area is occluded by windows"""
        if not self.auto_hide_enabled:
            return True  # Continue the timer

        if (
            self.is_mouse_over_panel_area
            or self._prevent_occlusion
        ):
            if not self.panel_revealer.get_reveal_child():
                self.panel_revealer.set_reveal_child(True)
            return True

        occlusion_region = ("top", self.panel_height)
        is_occluded_by_window = check_occlusion(occlusion_region)

        if is_occluded_by_window:
            if self.panel_revealer.get_reveal_child():
                self.panel_revealer.set_reveal_child(False)
        else:
            if not self.panel_revealer.get_reveal_child():
                self.panel_revealer.set_reveal_child(True)

        return True
