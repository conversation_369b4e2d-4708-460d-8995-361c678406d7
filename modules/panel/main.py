from fabric.system_tray.widgets import SystemTray, SystemTrayItem
from fabric.utils import exec_shell_command, get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.datetime import DateTime
from fabric.widgets.label import Label
from fabric.widgets.revealer import Revealer
from gi.repository import Gtk, GLib
from fabric.widgets.svg import Svg
from modules.panel.components.indicators import Indicators
from modules.panel.components.menubar import MenuBar
from utils.wayland import WaylandWindow as Window
from utils.occlusion import check_occlusion
import config.data as data

original_do_update_properties = SystemTrayItem.do_update_properties


def patched_do_update_properties(self, *_):
    # Try default GTK theme first
    icon_name = self._item.icon_name
    attention_icon_name = self._item.attention_icon_name

    if self._item.status == "NeedsAttention" and attention_icon_name:
        preferred_icon_name = attention_icon_name
    else:
        preferred_icon_name = icon_name

    # Try to load from default GTK theme
    if preferred_icon_name:
        try:
            default_theme = Gtk.IconTheme.get_default()
            if default_theme.has_icon(preferred_icon_name):
                pixbuf = default_theme.load_icon(
                    preferred_icon_name, self._icon_size, Gtk.IconLookupFlags.FORCE_SIZE
                )
                if pixbuf:
                    self._image.set_from_pixbuf(pixbuf)
                    # Set tooltip
                    tooltip = self._item.tooltip
                    self.set_tooltip_markup(
                        tooltip.description or tooltip.title or self._item.title.title()
                        if self._item.title
                        else "Unknown"
                    )
                    return
        except:
            pass
    original_do_update_properties(self, *_)


SystemTrayItem.do_update_properties = patched_do_update_properties


class Panel(Window):
    def __init__(
        self,
    ):
        super().__init__(
            name="bar",
            layer="top",
            anchor="left top right",
            exclusivity="auto",
            visible=False,
        )

        # Autohide functionality
        self.auto_hide_enabled = data.PANEL_AUTO_HIDE
        self.is_mouse_over_panel = False
        self.hide_timer_id = None
        self.show_timer_id = None
        self.panel_height = 32  # Default panel height, adjust as needed

        self.menubar = MenuBar()

        self.imac = Button(
            name="panel-button",
            child=Svg(
                size=24,
                svg_file=get_relative_path("../../config/assets/icons/logo.svg"),
            ),
            on_clicked=lambda *_: self.menubar.show_system_dropdown(self.imac)
        )
        self.notch_spot = Box(
            name="notch-spot",
            size=(200, 24),
            h_expand=True,
            v_expand=True,
            children=Label(label="notch"),
        )

        self.tray = SystemTray(name="system-tray", spacing=4, icon_size=20)

        self.tray_revealer = Revealer(
            name="tray-revealer",
            child=self.tray,
            child_revealed=False,
            transition_type="slide-left",
            transition_duration=300,
        )

        self.chevron_button = Button(
            name="panel-button",
            child=Svg(
                size=18,
                svg_file=get_relative_path("../../config/assets/icons/chevron-right.svg"),
            ),
            on_clicked=self.toggle_tray,
        )

        self.indicators = Indicators()

        self.search = Button(
            name="panel-button",
            on_clicked=lambda *_: self.search_apps(),
            child=Svg(
                size=20,
                svg_file=get_relative_path("../../config/assets/icons/search.svg"),
            ),
        )

        self.controlcenter = Button(
            name="panel-button",
            child=Svg(
                size=24,
                svg_file=get_relative_path("../../config/assets/icons/control-center.svg"),
            ),
        )

        self.children = CenterBox(
            name="panel",
            start_children=Box(
                name="modules-left",
                children=[
                    self.imac,
                    self.menubar,
                ],
            ),
            # center_children=Box(
            #     name="modules-center",
            #     children=self.notch_spot,
            # ),
            end_children=Box(
                name="modules-right",
                spacing=4,
                orientation="h",
                children=[
                    self.tray_revealer,
                    self.chevron_button,
                    self.indicators,
                    self.search,
                    self.controlcenter,
                    DateTime(name="date-time", formatters=["%a %b %d %I:%M"]),
                ],
            ),
        )

        # Set up autohide functionality
        if self.auto_hide_enabled:
            self.setup_autohide()

        return self.show_all()
    
    def search_apps(self):
        cmd = "fabric-cli exec modus 'launcher.show_launcher()'"
        exec_shell_command(cmd)

    def toggle_tray(self, *_):
        current_state = self.tray_revealer.child_revealed
        self.tray_revealer.child_revealed = not current_state

        if self.tray_revealer.child_revealed:
            self.chevron_button.get_child().set_from_file(get_relative_path("../../config/assets/icons/chevron-left.svg"))
        else:
            self.chevron_button.get_child().set_from_file(get_relative_path("../../config/assets/icons/chevron-right.svg"))

    def setup_autohide(self):
        """Set up autohide functionality for the panel"""
        # Connect mouse enter/leave events
        self.connect("enter-notify-event", self.on_mouse_enter)
        self.connect("leave-notify-event", self.on_mouse_leave)

        # Start the occlusion check timer
        GLib.timeout_add(500, self.check_occlusion_state)  # Check every 500ms

        # Initially hide the panel if autohide is enabled
        self.set_visible(False)

    def on_mouse_enter(self, widget, event):
        """Handle mouse entering the panel area"""
        if not self.auto_hide_enabled:
            return False

        self.is_mouse_over_panel = True

        # Cancel hide timer if it's running
        if self.hide_timer_id:
            GLib.source_remove(self.hide_timer_id)
            self.hide_timer_id = None

        # Show panel immediately when mouse enters
        if not self.get_visible():
            self.set_visible(True)

        return False

    def on_mouse_leave(self, widget, event):
        """Handle mouse leaving the panel area"""
        if not self.auto_hide_enabled:
            return False

        self.is_mouse_over_panel = False

        # Cancel show timer if it's running
        if self.show_timer_id:
            GLib.source_remove(self.show_timer_id)
            self.show_timer_id = None

        # Start hide timer
        if not self.hide_timer_id:
            self.hide_timer_id = GLib.timeout_add(1000, self.hide_panel_delayed)  # Hide after 1 second

        return False

    def hide_panel_delayed(self):
        """Hide the panel after a delay if mouse is not over it"""
        if not self.is_mouse_over_panel and self.auto_hide_enabled:
            self.set_visible(False)

        self.hide_timer_id = None
        return False  # Don't repeat the timer

    def check_occlusion_state(self):
        """Check if the panel area is occluded by windows"""
        if not self.auto_hide_enabled:
            return True  # Continue the timer

        # Check if the top area of the screen is occluded
        is_occluded = check_occlusion(("top", self.panel_height))

        if is_occluded and not self.is_mouse_over_panel:
            # Hide panel if occluded and mouse is not over it
            if self.get_visible():
                self.set_visible(False)
        elif not is_occluded and not self.get_visible():
            # Show panel if not occluded and currently hidden
            if not self.show_timer_id:
                self.show_timer_id = GLib.timeout_add(200, self.show_panel_delayed)  # Show after 200ms delay

        return True  # Continue the timer

    def show_panel_delayed(self):
        """Show the panel after a delay"""
        if self.auto_hide_enabled and not self.get_visible():
            # Double-check occlusion before showing
            if not check_occlusion(("top", self.panel_height)):
                self.set_visible(True)

        self.show_timer_id = None
        return False  # Don't repeat the timer
